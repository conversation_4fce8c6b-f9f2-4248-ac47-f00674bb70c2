<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备数据库表结构说明</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 2px 3px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .table-container {
            margin-bottom: 40px;
            overflow-x: auto;
        }
        .table-description {
            margin-bottom: 15px;
            font-style: italic;
            color: #555;
        }
    </style>
</head>
<body>
    <h1>DeviceDataSource 数据库表结构说明</h1>

    <div class="table-container">
        <h2>1. alarminfotable (设备报警信息表)</h2>
        <p class="table-description">该表存储设备报警相关信息，包括报警内容、持续时间等。</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>允许NULL</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>dataid</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>数据ID，主键</td>
                </tr>
                <tr>
                    <td>devno</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>设备号</td>
                </tr>
                <tr>
                    <td>alarmcontent</td>
                    <td>text</td>
                    <td>是</td>
                    <td>报警内容</td>
                </tr>
                <tr>
                    <td>alarmtime</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>报警持续时间，单位为分钟</td>
                </tr>
                <tr>
                    <td>createtime</td>
                    <td>datetime</td>
                    <td>是</td>
                    <td>数据创建时间</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-container">
        <h2>2. devinfotable (设备信息表)</h2>
        <p class="table-description">该表存储设备的详细信息，包括设备状态、加工参数、产量统计等实时数据。</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>允许NULL</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>devno</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>设备号，主键</td>
                </tr>
                <tr>
                    <td>devname</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>设备名称</td>
                </tr>
                <tr>
                    <td>devstate</td>
                    <td>nvarchar(2)</td>
                    <td>是</td>
                    <td>设备状态：0-离线，1-在线，2-空闲，3-报警。<br><strong>注意：</strong>读取状态前需先检查lastupdatetime距离当前是否大于5分钟，如果大于则直接判定为离线</td>
                </tr>
                <tr>
                    <td>lastupdatetime</td>
                    <td>datetime</td>
                    <td>是</td>
                    <td>最后更新时间，用于判断设备是否离线</td>
                </tr>
                <tr>
                    <td>devmodel</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>加工模式</td>
                </tr>
                <tr>
                    <td>spindlespeed</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>主轴转速</td>
                </tr>
                <tr>
                    <td>feedrate</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>进给速度</td>
                </tr>
                <tr>
                    <td>partcount</td>
                    <td>nvarchar(15)</td>
                    <td>是</td>
                    <td>总产量</td>
                </tr>
                <tr>
                    <td>exeprograme</td>
                    <td>nvarchar(15)</td>
                    <td>是</td>
                    <td>执行程序</td>
                </tr>
                <tr>
                    <td>spindleoverride</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>主轴倍率</td>
                </tr>
                <tr>
                    <td>feedrateoverride</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>进给倍率</td>
                </tr>
                <tr>
                    <td>toolnumber</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>刀具号</td>
                </tr>
                <tr>
                    <td>powerontime</td>
                    <td>nvarchar(20)</td>
                    <td>是</td>
                    <td>开机时间</td>
                </tr>
                <tr>
                    <td>oprtime</td>
                    <td>nvarchar(20)</td>
                    <td>是</td>
                    <td>运行时间</td>
                </tr>
                <tr>
                    <td>cuttime</td>
                    <td>nvarchar(20)</td>
                    <td>是</td>
                    <td>切削时间</td>
                </tr>
                <tr>
                    <td>cyctime</td>
                    <td>nvarchar(20)</td>
                    <td>是</td>
                    <td>循环时间</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-container">
        <h2>3. devlinkline (设备与生产线关联表)</h2>
        <p class="table-description">该表用于关联设备与生产线之间的关系。</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>允许NULL</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>dataid</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>数据ID，主键</td>
                </tr>
                <tr>
                    <td>devno</td>
                    <td>nvarchar(50)</td>
                    <td>是</td>
                    <td>设备号</td>
                </tr>
                <tr>
                    <td>lineid</td>
                    <td>nvarchar(50)</td>
                    <td>是</td>
                    <td>生产线ID</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-container">
        <h2>4. devproductcount (设备生产数据统计表)</h2>
        <p class="table-description">该表存储设备的生产数据统计信息，包括产量、完成率、设备效率等。</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>允许NULL</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>dataid</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>数据ID，主键</td>
                </tr>
                <tr>
                    <td>devno</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>设备号</td>
                </tr>
                <tr>
                    <td>productdate</td>
                    <td>nvarchar(15)</td>
                    <td>是</td>
                    <td>加工日期，格式如：2025-05-21</td>
                </tr>
                <tr>
                    <td>workclass</td>
                    <td>nvarchar(2)</td>
                    <td>是</td>
                    <td>工作班次：B-白班，Y-晚班</td>
                </tr>
                <tr>
                    <td>productcount</td>
                    <td>nvarchar(50)</td>
                    <td>是</td>
                    <td>产量</td>
                </tr>
                <tr>
                    <td>finishrate</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>完成率，如：80.2</td>
                </tr>
                <tr>
                    <td>devoee</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>设备OEE(设备综合效率)，如：80.5</td>
                </tr>
                <tr>
                    <td>poweronrate</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>开机率，如：60.0</td>
                </tr>
                <tr>
                    <td>createtime</td>
                    <td>datetime</td>
                    <td>是</td>
                    <td>数据创建时间</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-container">
        <h2>5. lineinfo (生产线信息表)</h2>
        <p class="table-description">该表存储生产线的基本信息。</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>允许NULL</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>lineid</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>生产线ID，主键</td>
                </tr>
                <tr>
                    <td>linename</td>
                    <td>nvarchar(50)</td>
                    <td>是</td>
                    <td>生产线名称</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-container">
        <h2>6. production_records (生产记录表)</h2>
        <p class="table-description">该表存储设备的详细生产加工记录，包括零件信息、加工参数、质量数据等。</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>允许NULL</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>id</td>
                    <td>INT IDENTITY(1,1)</td>
                    <td>否</td>
                    <td>记录ID，主键，自增</td>
                </tr>
                <tr>
                    <td>devno</td>
                    <td>nvarchar(50)</td>
                    <td>否</td>
                    <td>设备号</td>
                </tr>
                <tr>
                    <td>part_name</td>
                    <td>nvarchar(100)</td>
                    <td>是</td>
                    <td>零件名称，如：轴承座-1234</td>
                </tr>
                <tr>
                    <td>material</td>
                    <td>nvarchar(50)</td>
                    <td>是</td>
                    <td>材料类型，如：45#钢、不锈钢304等</td>
                </tr>
                <tr>
                    <td>process</td>
                    <td>nvarchar(50)</td>
                    <td>是</td>
                    <td>加工工艺，如：粗加工、精加工、钻孔等</td>
                </tr>
                <tr>
                    <td>quantity</td>
                    <td>INT</td>
                    <td>是</td>
                    <td>本次加工数量</td>
                </tr>
                <tr>
                    <td>cycle_time</td>
                    <td>DECIMAL(8,2)</td>
                    <td>是</td>
                    <td>单件加工时间（分钟）</td>
                </tr>
                <tr>
                    <td>spindle_speed</td>
                    <td>INT</td>
                    <td>是</td>
                    <td>主轴转速（rpm）</td>
                </tr>
                <tr>
                    <td>feed_rate</td>
                    <td>INT</td>
                    <td>是</td>
                    <td>进给速度（mm/min）</td>
                </tr>
                <tr>
                    <td>cutting_depth</td>
                    <td>DECIMAL(8,2)</td>
                    <td>是</td>
                    <td>切削深度（mm）</td>
                </tr>
                <tr>
                    <td>coolant_flow</td>
                    <td>INT</td>
                    <td>是</td>
                    <td>冷却液流量（L/min）</td>
                </tr>
                <tr>
                    <td>tool_wear</td>
                    <td>DECIMAL(8,3)</td>
                    <td>是</td>
                    <td>刀具磨损度（0-1）</td>
                </tr>
                <tr>
                    <td>surface_roughness</td>
                    <td>DECIMAL(8,2)</td>
                    <td>是</td>
                    <td>表面粗糙度（μm）</td>
                </tr>
                <tr>
                    <td>dimension_tolerance</td>
                    <td>DECIMAL(8,3)</td>
                    <td>是</td>
                    <td>尺寸公差（mm）</td>
                </tr>
                <tr>
                    <td>quality_grade</td>
                    <td>nvarchar(10)</td>
                    <td>是</td>
                    <td>质量等级：A-优秀，B-良好，C-合格</td>
                </tr>
                <tr>
                    <td>operator</td>
                    <td>nvarchar(50)</td>
                    <td>是</td>
                    <td>操作员</td>
                </tr>
                <tr>
                    <td>shift</td>
                    <td>nvarchar(20)</td>
                    <td>是</td>
                    <td>班次：B-白班，Y-夜班</td>
                </tr>
                <tr>
                    <td>start_time</td>
                    <td>DATETIME</td>
                    <td>是</td>
                    <td>加工开始时间</td>
                </tr>
                <tr>
                    <td>end_time</td>
                    <td>DATETIME</td>
                    <td>是</td>
                    <td>加工结束时间</td>
                </tr>
                <tr>
                    <td>create_time</td>
                    <td>DATETIME</td>
                    <td>是</td>
                    <td>记录创建时间，默认GETDATE()</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-container">
        <h2>7. userinfotable (用户信息表)</h2>
        <p class="table-description">该表存储系统用户信息，包括用户名、密码、权限等。</p>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>数据类型</th>
                    <th>允许NULL</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>dataid</td>
                    <td>varchar(50)</td>
                    <td>否</td>
                    <td>数据ID，也是用户唯一标识</td>
                </tr>
                <tr>
                    <td>username</td>
                    <td>varchar(50)</td>
                    <td>否</td>
                    <td>用户名</td>
                </tr>
                <tr>
                    <td>userpwd</td>
                    <td>varchar(50)</td>
                    <td>是</td>
                    <td>用户密码</td>
                </tr>
                <tr>
                    <td>isadmin</td>
                    <td>nvarchar(2)</td>
                    <td>是</td>
                    <td>是否管理员：Y-管理员，N-普通用户。管理员可以导出数据，进入后台，管理登录用户</td>
                </tr>
                <tr>
                    <td>createtime</td>
                    <td>datetime</td>
                    <td>是</td>
                    <td>创建时间</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-container">
        <h2>表间关系说明</h2>
        <p class="table-description">以下是各表之间的关联关系：</p>
        <ul>
            <li><strong>devinfotable</strong> 和 <strong>alarminfotable</strong>: 通过 <code>devno</code> 字段关联，一个设备可以有多条报警记录</li>
            <li><strong>devinfotable</strong> 和 <strong>devproductcount</strong>: 通过 <code>devno</code> 字段关联，一个设备可以有多条生产数据记录</li>
            <li><strong>devinfotable</strong> 和 <strong>production_records</strong>: 通过 <code>devno</code> 字段关联，一个设备可以有多条详细生产记录</li>
            <li><strong>lineinfo</strong> 和 <strong>devlinkline</strong>: 通过 <code>lineid</code> 字段关联，一条生产线可以关联多个设备</li>
            <li><strong>devinfotable</strong> 和 <strong>devlinkline</strong>: 通过 <code>devno</code> 字段关联，一个设备可以属于一条生产线</li>
        </ul>

        <h3>重要业务规则</h3>
        <ul>
            <li><strong>设备状态判断</strong>: 读取 <code>devinfotable.devstate</code> 前，必须先检查 <code>lastupdatetime</code> 是否超过5分钟，超过则直接判定为离线</li>
            <li><strong>班次管理</strong>: <code>devproductcount.workclass</code> 和 <code>production_records.shift</code> 使用相同的编码规则：B-白班，Y-夜班</li>
            <li><strong>数据完整性</strong>: <code>production_records</code> 表包含详细的加工参数和质量数据，用于生产分析和质量追溯</li>
            <li><strong>字段长度限制</strong>: 所有 nvarchar 字段都有长度限制，插入数据时需注意字符串截断问题</li>
        </ul>
    </div>
</body>
</html>