#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版产量更新脚本
快速测试和更新devproductcount表的productcount字段
"""

import pyodbc
import time
from datetime import datetime

def get_connection():
    """获取数据库连接"""
    drivers = ['{SQL Server}', '{SQL Server Native Client 11.0}']

    # 尝试Windows身份验证
    for driver in drivers:
        try:
            conn_str = f"DRIVER={driver};SERVER=localhost;DATABASE=DeviceDataSource;Trusted_Connection=yes;"
            conn = pyodbc.connect(conn_str)
            print(f"✓ Windows身份验证连接成功，使用驱动: {driver}")
            return conn
        except Exception as e:
            print(f"✗ Windows身份验证 {driver} 失败: {e}")

    # 尝试SQL Server身份验证
    for driver in drivers:
        try:
            conn_str = f"DRIVER={driver};SERVER=localhost;DATABASE=DeviceDataSource;UID=sa;PWD=sasasa;TrustServerCertificate=yes;"
            conn = pyodbc.connect(conn_str)
            print(f"✓ SQL Server身份验证连接成功，使用驱动: {driver}")
            return conn
        except Exception as e:
            print(f"✗ SQL Server身份验证 {driver} 失败: {e}")

    return None

def main():
    """主函数"""
    target_date = '2025-05-25'
    print(f"开始更新 {target_date} 的产量数据...")

    try:
        conn = get_connection()
        if not conn:
            print("❌ 数据库连接失败")
            return

        cursor = conn.cursor()

        # 查看当前数据
        cursor.execute("""
            SELECT dataid, devno, productcount, workclass
            FROM devproductcount
            WHERE productdate = ?
            ORDER BY devno
        """, target_date)

        records = cursor.fetchall()
        print(f"\n找到 {len(records)} 条记录:")

        if not records:
            print("没有找到记录，正在创建测试数据...")
            # 创建测试记录
            test_devices = ['test01', 'test02', 'test03']
            for devno in test_devices:
                for workclass in ['B', 'Y']:
                    dataid = f"{devno}_{target_date}_{workclass}"
                    cursor.execute("""
                        INSERT INTO devproductcount
                        (dataid, devno, productdate, workclass, productcount, createtime)
                        VALUES (?, ?, ?, ?, '0', GETDATE())
                    """, dataid, devno, target_date, workclass)

            conn.commit()
            print("✓ 创建了测试数据")

            # 重新查询
            cursor.execute("""
                SELECT dataid, devno, productcount, workclass
                FROM devproductcount
                WHERE productdate = ?
                ORDER BY devno
            """, target_date)
            records = cursor.fetchall()

        # 开始循环更新
        round_count = 0
        while True:
            round_count += 1
            print(f"\n=== 第 {round_count} 轮更新 [{datetime.now().strftime('%H:%M:%S')}] ===")

            for record in records:
                dataid, devno, productcount, workclass = record

                # 获取当前值
                current_count = 0
                if productcount:
                    try:
                        current_count = int(str(productcount).strip())
                    except:
                        current_count = 0

                # 增加1
                new_count = current_count + 1

                # 更新产量数据
                cursor.execute("""
                    UPDATE devproductcount
                    SET productcount = ?, createtime = GETDATE()
                    WHERE dataid = ?
                """, str(new_count), dataid)

                # 同时更新设备的lastupdatetime，确保设备显示在线
                cursor.execute("""
                    UPDATE devinfotable
                    SET lastupdatetime = GETDATE(), devstate = '1'
                    WHERE devno = ?
                """, devno)

                print(f"设备 {devno} ({workclass}班): {current_count} → {new_count} (设备状态已更新为在线)")

            conn.commit()
            print("✓ 更新完成，等待3秒...")

            # 更新records中的productcount值，避免重复查询
            records = [(dataid, devno, str(int(str(productcount or '0').strip()) + 1), workclass)
                      for dataid, devno, productcount, workclass in records]

            time.sleep(3)

    except KeyboardInterrupt:
        print("\n\n收到停止信号，正在退出...")
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("数据库连接已关闭")

if __name__ == "__main__":
    main()
