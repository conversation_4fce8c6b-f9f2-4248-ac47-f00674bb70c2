<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华伍股份轨交车间数字化管理系统</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body>
    <!-- 背景Three.js容器 -->
    <div id="three-bg"></div>

    <!-- 主要内容容器 -->
    <div class="dashboard-container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
                <div class="weather-info" id="weather-info">
                    <span class="weather-location" id="weather-location">获取位置中...</span>
                    <span class="weather-temp" id="weather-temp">--°C</span>
                    <span class="weather-desc" id="weather-desc">--</span>
                </div>
            </div>
            <h1 class="main-title">华伍股份轨交车间数字化管理系统</h1>
            <div class="header-right">
                <div class="current-time" id="current-time">
                    <span class="date" id="current-date">--</span>
                    <span class="time" id="current-time-display">--:--:--</span>
                </div>
                <div class="admin-link-container">
                    <a href="/admin" class="admin-link" title="后台管理">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        <span>后台管理</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧数据分析区域 -->
            <section class="left-panel">
                <div class="panel-card">
                    <h3 class="panel-title">🔍 设备状态分析</h3>
                    <div class="efficiency-content">
                        <div class="efficiency-chart-container">
                            <canvas id="efficiencyChart" width="220" height="220"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel-card production-stats-card">
                    <h3 class="panel-title">📊 生产统计</h3>
                    <div class="production-stats" id="production-stats">
                        <div class="loading">正在加载生产统计数据...</div>
                    </div>
                </div>

                <div class="panel-card chart-card">
                    <h3 class="panel-title">📈 七日产量</h3>
                    <div class="chart-container">
                        <canvas id="trendChart" width="300" height="200"></canvas>
                    </div>
                </div>
            </section>

            <!-- 中央主要显示区域 -->
            <section class="center-panel">
                <!-- 统计数据卡片 -->
                <div class="stats-cards">
                    <div class="stat-card running">
                        <h4>运行数量</h4>
                        <span class="stat-number" id="running-count">-</span>
                    </div>
                    <div class="stat-card idle">
                        <h4>空闲数量</h4>
                        <span class="stat-number" id="idle-count">-</span>
                    </div>
                    <div class="stat-card alarm">
                        <h4>报警数量</h4>
                        <span class="stat-number" id="alarm-count">-</span>
                    </div>
                    <div class="stat-card offline">
                        <h4>离线数量</h4>
                        <span class="stat-number" id="offline-count">-</span>
                    </div>
                </div>

                <!-- 设备卡片区域 -->
                <div class="device-cards-section">
                    <div class="device-section-header">
                        <!-- 左侧空白区域 -->
                        <div class="header-spacer"></div>
                        <!-- 右侧控制区域 -->
                        <div class="header-controls">
                            <div class="line-selector-compact">
                                <label for="production-line-select" class="line-selector-label">生产线：</label>
                                <select id="production-line-select" class="line-select-small">
                                    <option value="all">全部生产线</option>
                                </select>
                            </div>
                            <button id="refresh-data-btn" class="refresh-btn-small" title="刷新数据">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="23 4 23 10 17 10"></polyline>
                                    <polyline points="1 20 1 14 7 14"></polyline>
                                    <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="device-grid" id="device-grid">
                        <div class="loading-card">正在加载设备数据...</div>
                    </div>
                </div>
            </section>

            <!-- 右侧信息面板 -->
            <section class="right-panel">
                <!-- 日历移到最上面 -->
                <div class="panel-card calendar-panel-card">
                    <h3 class="panel-title">📅 安全生产日历</h3>
                    <div class="safety-calendar-container">
                        <div class="calendar-header">
                            <button id="prev-month" class="calendar-nav-btn">‹</button>
                            <span id="current-month-year" class="calendar-title">2024年1月</span>
                            <button id="next-month" class="calendar-nav-btn">›</button>
                        </div>
                        <div class="calendar-grid">
                            <div class="calendar-weekdays">
                                <div class="weekday">日</div>
                                <div class="weekday">一</div>
                                <div class="weekday">二</div>
                                <div class="weekday">三</div>
                                <div class="weekday">四</div>
                                <div class="weekday">五</div>
                                <div class="weekday">六</div>
                            </div>
                            <div class="calendar-days" id="calendar-days">
                                <!-- 日期将通过JavaScript动态生成 -->
                            </div>
                        </div>
                        <div class="calendar-legend">
                            <div class="legend-item">
                                <div class="legend-color normal"></div>
                                <span>正常</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color alarm"></div>
                                <span>有报警</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color today"></div>
                                <span>今天</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 报警信息移到下面 -->
                <div class="panel-card alert-info-card">
                    <h3 class="panel-title">🚨 最新报警信息</h3>
                    <div class="latest-info-list" id="latest-info">
                        <div class="loading">正在加载最新信息...</div>
                    </div>
                </div>
            </section>
        </main>


    </div>

    <!-- 异常信息弹窗 -->
    <div id="alarm-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">2024年1月15日 - 异常报告</h3>
                <span class="close" id="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alarm-summary">
                    <div class="summary-card">
                        <h4>📊 异常统计</h4>
                        <div class="alarm-stats">
                            <div class="stat-item-modal">
                                <span class="label">总报警数</span>
                                <span class="value" id="total-alarms">-</span>
                            </div>
                            <div class="stat-item-modal">
                                <span class="label">涉及设备</span>
                                <span class="value" id="affected-devices">-</span>
                            </div>
                            <div class="stat-item-modal">
                                <span class="label">持续时间</span>
                                <span class="value" id="total-duration">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alarm-chart-section">
                    <h4>📈 报警趋势图</h4>
                    <div class="chart-container-modal">
                        <canvas id="alarmTrendChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="alarm-details">
                    <h4>🔍 详细报警信息</h4>
                    <div class="alarm-list" id="alarm-list">
                        <div class="loading">正在加载报警详情...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    
    <!-- 强制修复四个状态卡片布局 -->
    <script>
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 强制设置stats-cards为flex布局
            const statsCards = document.querySelector('.stats-cards');
            if (statsCards) {
                // 直接设置内联样式，优先级最高
                statsCards.style.cssText = `
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: stretch !important;
                    gap: 10px !important;
                    width: 100% !important;
                    flex-wrap: nowrap !important;
                `;
            }
            
            // 设置每个stat-card的样式
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.style.cssText += `
                    flex: 1 1 0 !important;
                    min-width: 0 !important;
                    max-width: calc(25% - 8px) !important;
                    width: auto !important;
                `;
            });
        });
</body>
</html>