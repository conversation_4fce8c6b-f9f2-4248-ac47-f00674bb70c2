@echo off
:: 设置代码页为UTF-8，解决中文乱码问题
chcp 65001 >nul 2>&1

:: 设置窗口标题
title 华伍股份轨交车间数字化管理系统 - 部署脚本

:: 设置控制台颜色 (蓝色背景白色字体)
color 1F

echo.
echo ==========================================
echo     华伍股份轨交车间数字化管理系统
echo     Production Workshop Monitoring System
echo              部署脚本 v1.0
echo ==========================================
echo.

:: 显示当前时间和系统信息
echo [%date% %time%] 开始部署向导...
echo 系统信息: %OS% %PROCESSOR_ARCHITECTURE%
echo 当前目录: %CD%
echo.

:: 检查管理员权限
echo [权限检查] 验证管理员权限...
net session >nul 2>&1
if %errorLevel% equ 0 (
    echo    [成功] 管理员权限检查通过
) else (
    echo.
    echo [错误] 需要管理员权限
    echo.
    echo 解决方法:
    echo 1. 右键点击此脚本
    echo 2. 选择 "以管理员身份运行"
    echo 3. 在UAC提示中点击 "是"
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo.

:MENU
echo ==========================================
echo              部署选项菜单
echo ==========================================
echo.
echo 请选择部署操作:
echo.
echo   [1] 完整部署 (推荐新用户)
echo       - 检查环境
echo       - 安装依赖
echo       - 配置数据库
echo       - 启动服务
echo.
echo   [2] 仅安装依赖包
echo       - 安装Python依赖
echo       - 检查ODBC驱动
echo.
echo   [3] 仅启动服务
echo       - 启动主服务和管理服务
echo.
echo   [4] 配置数据库连接
echo       - 测试数据库连接
echo       - 修改连接配置
echo.
echo   [5] 创建Windows服务
echo       - 安装为系统服务
echo       - 开机自启动
echo.
echo   [6] 系统诊断
echo       - 环境检查
echo       - 问题诊断
echo.
echo   [0] 退出部署脚本
echo.
echo ==========================================

set /p choice=请输入选项编号 (0-6):

:: 验证输入
if "%choice%"=="1" goto FULL_DEPLOY
if "%choice%"=="2" goto INSTALL_DEPS
if "%choice%"=="3" goto START_SERVICE
if "%choice%"=="4" goto CONFIG_DB
if "%choice%"=="5" goto CREATE_SERVICE
if "%choice%"=="6" goto SYSTEM_DIAG
if "%choice%"=="0" goto EXIT

echo.
echo [错误] 无效的选项: %choice%
echo 请输入 0-6 之间的数字
echo.
pause
goto MENU

:FULL_DEPLOY
echo.
echo ==========================================
echo            开始完整部署
echo ==========================================
echo.
echo [信息] 完整部署包含以下步骤:
echo   1. 环境检查
echo   2. 安装依赖包
echo   3. 配置数据库连接
echo   4. 启动服务
echo.
set /p confirm=确认开始完整部署? (Y/N):
if /i not "%confirm%"=="Y" goto MENU

echo.
echo [%date% %time%] 开始完整部署...
call :SYSTEM_CHECK
if %errorLevel% neq 0 goto MENU
call :INSTALL_DEPS
if %errorLevel% neq 0 goto MENU
call :CONFIG_DB
if %errorLevel% neq 0 goto MENU
call :START_SERVICE
echo.
echo ==========================================
echo           完整部署成功完成!
echo ==========================================
goto END

:SYSTEM_CHECK
echo.
echo [环境检查] 检查系统环境...

:: 检查Python
echo    检查Python环境...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo    [错误] Python未安装或未添加到PATH
    echo.
    echo    请执行以下步骤:
    echo    1. 访问: https://www.python.org/downloads/
    echo    2. 下载Python 3.8或更高版本
    echo    3. 安装时勾选 "Add Python to PATH"
    echo    4. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo    [成功] Python %PYTHON_VERSION% 已安装

:: 检查pip
echo    检查pip包管理器...
pip --version >nul 2>&1
if %errorLevel% neq 0 (
    echo    [错误] pip未安装或不可用
    echo    请重新安装Python并确保包含pip
    pause
    exit /b 1
)
echo    [成功] pip可用

:: 检查必要文件
echo    检查项目文件...
if not exist "app.py" (
    echo    [错误] 缺少主服务文件: app.py
    pause
    exit /b 1
)
if not exist "admin_app.py" (
    echo    [错误] 缺少管理服务文件: admin_app.py
    pause
    exit /b 1
)
if not exist "index.html" (
    echo    [错误] 缺少前端文件: index.html
    pause
    exit /b 1
)
echo    [成功] 项目文件完整

echo    [成功] 系统环境检查通过
exit /b 0

:INSTALL_DEPS
echo.
echo [依赖安装] 安装Python依赖包...

echo    检查当前已安装的包...
pip list | findstr flask >nul 2>&1
if %errorLevel% equ 0 (
    echo    [信息] Flask已安装
) else (
    echo    [信息] Flask未安装，将进行安装
)

echo    开始安装依赖包...
echo    - flask: Web框架
echo    - flask-cors: 跨域支持
echo    - pyodbc: 数据库连接

pip install flask flask-cors pyodbc --upgrade --quiet
if %errorLevel% neq 0 (
    echo.
    echo    [错误] 依赖包安装失败
    echo.
    echo    可能的解决方案:
    echo    1. 检查网络连接
    echo    2. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask flask-cors pyodbc
    echo    3. 手动安装各个包
    echo.
    pause
    exit /b 1
)

echo    验证安装结果...
python -c "import flask, flask_cors, pyodbc; print('所有依赖包导入成功')" 2>nul
if %errorLevel% neq 0 (
    echo    [错误] 依赖包验证失败
    pause
    exit /b 1
)

echo    [成功] 依赖包安装完成
exit /b 0

:CONFIG_DB
echo.
echo [数据库配置] 配置和测试数据库连接...

echo    当前数据库配置:
echo    - 服务器地址: localhost
echo    - 数据库名称: DeviceDataSource
echo    - 认证方式: Windows认证 (当前用户)
echo    - ODBC驱动: SQL Server ODBC Driver
echo.

echo    测试数据库连接...
python -c "import pyodbc; conn=pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost;DATABASE=DeviceDataSource;Trusted_Connection=yes;'); print('数据库连接成功'); conn.close()" 2>nul
if %errorLevel% equ 0 (
    echo    [成功] 数据库连接测试通过
) else (
    echo    [警告] 数据库连接测试失败
    echo.
    echo    可能的原因:
    echo    1. SQL Server服务未启动
    echo    2. 数据库 DeviceDataSource 不存在
    echo    3. 当前用户无数据库访问权限
    echo    4. ODBC驱动未安装
    echo.
    echo    解决建议:
    echo    1. 启动SQL Server服务
    echo    2. 创建数据库或修改配置中的数据库名
    echo    3. 为当前用户授予数据库权限
    echo    4. 安装Microsoft ODBC Driver for SQL Server
    echo.
    set /p continue=是否继续部署? (Y/N):
    if /i not "%continue%"=="Y" (
        echo    用户选择停止部署
        exit /b 1
    )
)

echo    检查数据库配置文件...
if exist "app.py" (
    findstr "trusted_connection" app.py >nul 2>&1
    if %errorLevel% equ 0 (
        echo    [成功] app.py 已配置Windows认证
    ) else (
        echo    [警告] app.py 可能未正确配置Windows认证
    )
)

if exist "admin_app.py" (
    findstr "trusted_connection" admin_app.py >nul 2>&1
    if %errorLevel% equ 0 (
        echo    [成功] admin_app.py 已配置Windows认证
    ) else (
        echo    [警告] admin_app.py 可能未正确配置Windows认证
    )
)

echo.
echo    是否需要修改数据库配置?
echo    1. 使用当前配置 (localhost, Windows认证)
echo    2. 修改服务器地址
echo    3. 查看配置文件位置
echo.
set /p db_choice=请选择 (1-3):

if "%db_choice%"=="2" (
    echo.
    set /p new_server=请输入SQL Server地址 (如: *************):
    echo    [信息] 需要手动修改以下文件中的服务器地址:
    echo    - app.py (第15行左右)
    echo    - admin_app.py (第15行左右)
    echo    将 'localhost' 改为 '%new_server%'
    echo.
    pause
)

if "%db_choice%"=="3" (
    echo.
    echo    配置文件位置:
    echo    - 主服务配置: %CD%\app.py
    echo    - 管理服务配置: %CD%\admin_app.py
    echo.
    echo    配置示例:
    echo    DB_CONFIG = {
    echo        'server': 'localhost',
    echo        'database': 'DeviceDataSource',
    echo        'driver': '{ODBC Driver 17 for SQL Server}',
    echo        'trusted_connection': 'yes'
    echo    }
    echo.
    pause
)

echo    [成功] 数据库配置完成
exit /b 0

:START_SERVICE
echo.
echo [服务启动] 启动应用服务...

:: 检查端口占用
echo    检查端口占用情况...
netstat -an | findstr ":5000" >nul 2>&1
if %errorLevel% equ 0 (
    echo    [警告] 端口5000已被占用
    set /p kill5000=是否停止占用进程? (Y/N):
    if /i "%kill5000%"=="Y" (
        for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000') do (
            taskkill /f /pid %%a >nul 2>&1
        )
        echo    [信息] 端口5000已释放
    )
)

netstat -an | findstr ":5001" >nul 2>&1
if %errorLevel% equ 0 (
    echo    [警告] 端口5001已被占用
    set /p kill5001=是否停止占用进程? (Y/N):
    if /i "%kill5001%"=="Y" (
        for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5001') do (
            taskkill /f /pid %%a >nul 2>&1
        )
        echo    [信息] 端口5001已释放
    )
)

echo    启动主服务 (端口5000)...
start "生产车间监控系统-主服务" cmd /k "chcp 65001 >nul && echo [主服务] 正在启动... && python app.py"

:: 等待主服务启动
timeout /t 3 >nul

echo    启动管理服务 (端口5001)...
start "生产车间监控系统-管理服务" cmd /k "chcp 65001 >nul && echo [管理服务] 正在启动... && python admin_app.py"

:: 等待管理服务启动
timeout /t 3 >nul

echo    验证服务启动状态...
timeout /t 2 >nul

netstat -an | findstr ":5000" >nul 2>&1
if %errorLevel% equ 0 (
    echo    [成功] 主服务 (端口5000) 启动成功
) else (
    echo    [警告] 主服务 (端口5000) 可能启动失败
)

netstat -an | findstr ":5001" >nul 2>&1
if %errorLevel% equ 0 (
    echo    [成功] 管理服务 (端口5001) 启动成功
) else (
    echo    [警告] 管理服务 (端口5001) 可能启动失败
)

echo.
echo    [成功] 服务启动完成
echo.
echo    访问地址:
echo    - 主监控页面: http://localhost:5000
echo    - 管理后台:   http://localhost:5001
echo.
echo    重要提示:
echo    1. 两个服务窗口已自动打开
echo    2. 关闭窗口即可停止对应服务
echo    3. 如需停止所有服务，运行 stop.bat
echo.

set /p open_browser=是否自动打开浏览器? (Y/N):
if /i "%open_browser%"=="Y" (
    echo    正在打开浏览器...
    start http://localhost:5000
)

exit /b 0

:CREATE_SERVICE
echo.
echo [Windows服务] 创建系统服务...
echo.
echo [信息] 此功能将把应用安装为Windows系统服务
echo        服务将在系统启动时自动运行
echo.
set /p confirm_service=确认创建Windows服务? (Y/N):
if /i not "%confirm_service%"=="Y" goto MENU

echo    安装pywin32依赖...
pip install pywin32 --quiet
if %errorLevel% neq 0 (
    echo    [错误] pywin32安装失败
    echo    请检查网络连接或手动安装: pip install pywin32
    pause
    goto MENU
)

echo    创建服务配置文件...
call :CREATE_SERVICE_FILE

echo.
echo    [成功] Windows服务配置完成
echo.
echo    服务管理命令:
echo    - 安装服务: python service.py install
echo    - 启动服务: python service.py start
echo    - 停止服务: python service.py stop
echo    - 删除服务: python service.py remove
echo.
echo    服务信息:
echo    - 服务名称: DeviceMonitorService
echo    - 显示名称: 华伍股份轨交车间数字化管理系统
echo    - 启动类型: 自动启动
echo.

set /p install_now=是否立即安装并启动服务? (Y/N):
if /i "%install_now%"=="Y" (
    echo    正在安装服务...
    python service.py install
    if %errorLevel% equ 0 (
        echo    [成功] 服务安装完成
        echo    正在启动服务...
        python service.py start
        if %errorLevel% equ 0 (
            echo    [成功] 服务启动完成
        ) else (
            echo    [警告] 服务启动失败，请检查日志
        )
    ) else (
        echo    [错误] 服务安装失败
    )
)

exit /b 0

:SYSTEM_DIAG
echo.
echo [系统诊断] 检查系统环境和配置...
echo.

echo    === Python环境 ===
python --version 2>nul
if %errorLevel% neq 0 (
    echo    [错误] Python未安装
) else (
    echo    [成功] Python已安装
)

echo.
echo    === 依赖包检查 ===
python -c "import flask; print('Flask:', flask.__version__)" 2>nul
if %errorLevel% neq 0 (echo    [错误] Flask未安装) else (echo    [成功] Flask已安装)

python -c "import flask_cors; print('Flask-CORS: 已安装')" 2>nul
if %errorLevel% neq 0 (echo    [错误] Flask-CORS未安装) else (echo    [成功] Flask-CORS已安装)

python -c "import pyodbc; print('PyODBC: 已安装')" 2>nul
if %errorLevel% neq 0 (echo    [错误] PyODBC未安装) else (echo    [成功] PyODBC已安装)

echo.
echo    === 文件检查 ===
if exist "app.py" (echo    [成功] app.py 存在) else (echo    [错误] app.py 缺失)
if exist "admin_app.py" (echo    [成功] admin_app.py 存在) else (echo    [错误] admin_app.py 缺失)
if exist "index.html" (echo    [成功] index.html 存在) else (echo    [错误] index.html 缺失)
if exist "style.css" (echo    [成功] style.css 存在) else (echo    [错误] style.css 缺失)
if exist "script.js" (echo    [成功] script.js 存在) else (echo    [错误] script.js 缺失)

echo.
echo    === 端口检查 ===
netstat -an | findstr ":5000" >nul 2>&1
if %errorLevel% equ 0 (echo    [警告] 端口5000被占用) else (echo    [成功] 端口5000可用)

netstat -an | findstr ":5001" >nul 2>&1
if %errorLevel% equ 0 (echo    [警告] 端口5001被占用) else (echo    [成功] 端口5001可用)

echo.
echo    === 数据库连接测试 ===
python -c "import pyodbc; pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost;DATABASE=DeviceDataSource;Trusted_Connection=yes;')" 2>nul
if %errorLevel% equ 0 (echo    [成功] 数据库连接正常) else (echo    [警告] 数据库连接失败)

echo.
echo    === 系统信息 ===
echo    操作系统: %OS%
echo    处理器架构: %PROCESSOR_ARCHITECTURE%
echo    当前用户: %USERNAME%
echo    当前目录: %CD%

echo.
echo [诊断完成] 按任意键返回主菜单...
pause >nul
goto MENU

:CREATE_SERVICE_FILE
echo    创建Windows服务配置文件 service.py...
(
echo import win32serviceutil
echo import win32service
echo import win32event
echo import subprocess
echo import os
echo import sys
echo import time
echo.
echo class DeviceMonitorService^(win32serviceutil.ServiceFramework^):
echo     _svc_name_ = "DeviceMonitorService"
echo     _svc_display_name_ = "华伍股份轨交车间数字化管理系统"
echo     _svc_description_ = "华伍股份轨交车间数字化管理系统"
echo.
echo     def __init__^(self, args^):
echo         win32serviceutil.ServiceFramework.__init__^(self, args^)
echo         self.hWaitStop = win32event.CreateEvent^(None, 0, 0, None^)
echo         self.main_process = None
echo         self.admin_process = None
echo.
echo     def SvcStop^(self^):
echo         self.ReportServiceStatus^(win32service.SERVICE_STOP_PENDING^)
echo         try:
echo             if self.main_process:
echo                 self.main_process.terminate^(^)
echo             if self.admin_process:
echo                 self.admin_process.terminate^(^)
echo         except:
echo             pass
echo         win32event.SetEvent^(self.hWaitStop^)
echo.
echo     def SvcDoRun^(self^):
echo         try:
echo             # 设置工作目录
echo             os.chdir^(os.path.dirname^(os.path.abspath^(__file__^)^)^)
echo
echo             # 启动主服务
echo             self.main_process = subprocess.Popen^([
echo                 sys.executable, "app.py"
echo             ], cwd=os.getcwd^(^)^)
echo
echo             # 等待一秒
echo             time.sleep^(1^)
echo
echo             # 启动管理服务
echo             self.admin_process = subprocess.Popen^([
echo                 sys.executable, "admin_app.py"
echo             ], cwd=os.getcwd^(^)^)
echo
echo             # 等待停止信号
echo             win32event.WaitForSingleObject^(self.hWaitStop, win32event.INFINITE^)
echo         except Exception as e:
echo             # 记录错误到事件日志
echo             import servicemanager
echo             servicemanager.LogErrorMsg^(str^(e^)^)
echo.
echo if __name__ == '__main__':
echo     win32serviceutil.HandleCommandLine^(DeviceMonitorService^)
) > service.py
echo    [成功] service.py 创建完成
exit /b 0

:EXIT
echo.
echo ==========================================
echo           退出部署脚本
echo ==========================================
echo.
echo [%date% %time%] 部署脚本结束
echo.
echo 感谢使用华伍股份轨交车间数字化管理系统!
echo.
echo 如需帮助，请查看:
echo - 部署说明.md
echo - start.bat (快速启动)
echo - stop.bat (停止服务)
echo.
pause
exit /b 0

:END
echo.
echo ==========================================
echo           部署成功完成!
echo ==========================================
echo.
echo [%date% %time%] 部署完成
echo.
echo 重要提醒:
echo 1. 确保SQL Server服务正在运行
echo 2. 确保当前Windows用户有数据库访问权限
echo 3. 如需修改服务器地址，请编辑app.py和admin_app.py
echo 4. 防火墙可能需要开放端口5000和5001
echo.
echo 故障排除:
echo - 连接失败: 检查SQL Server配置和权限
echo - 端口占用: 运行stop.bat或修改端口号
echo - 依赖问题: 重新运行选项2安装依赖
echo - 详细错误: 查看服务窗口的控制台输出
echo.
echo 常用操作:
echo - 快速启动: 运行 start.bat
echo - 停止服务: 运行 stop.bat
echo - 重新部署: 重新运行此脚本
echo - 系统诊断: 选择选项6
echo.
echo 访问地址:
echo - 主监控页面: http://localhost:5000
echo - 管理后台:   http://localhost:5001
echo.

set /p final_action=选择操作: [1]打开浏览器 [2]查看服务状态 [3]退出 (1-3):

if "%final_action%"=="1" (
    echo 正在打开浏览器...
    start http://localhost:5000
)

if "%final_action%"=="2" (
    echo.
    echo 检查服务状态...
    netstat -an | findstr ":5000"
    netstat -an | findstr ":5001"
    echo.
    pause
)

echo.
echo 部署脚本结束，感谢使用!
pause
