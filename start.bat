@echo off
:: 设置代码页为UTF-8，解决中文乱码问题
chcp 65001 >nul 2>&1

:: 设置窗口标题
title 华伍股份轨交车间数字化管理系统 - 启动脚本

:: 设置控制台颜色 (绿色背景黑色字体)
color 0A

echo.
echo ==========================================
echo     华伍股份轨交车间数字化管理系统
echo     Production Workshop Monitoring System
echo ==========================================
echo.

:: 显示当前时间
echo [%date% %time%] 开始启动系统...
echo.

:: 检查Python环境
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo.
    echo [错误] Python未安装或未添加到PATH环境变量
    echo.
    echo 请执行以下步骤:
    echo 1. 下载Python 3.8+: https://www.python.org/downloads/
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

:: 显示Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo    Python版本: %PYTHON_VERSION%
echo    [成功] Python环境检查通过
echo.

:: 检查依赖包
echo [2/4] 检查Python依赖包...
python -c "import flask, flask_cors, pyodbc" >nul 2>&1
if %errorLevel% neq 0 (
    echo    [警告] 依赖包缺失，正在自动安装...
    echo.
    echo    安装中: flask flask-cors pyodbc
    pip install flask flask-cors pyodbc --quiet
    if %errorLevel% neq 0 (
        echo.
        echo [错误] 依赖包安装失败
        echo.
        echo 请手动执行: pip install flask flask-cors pyodbc
        echo.
        pause
        exit /b 1
    )
    echo    [成功] 依赖包安装完成
) else (
    echo    [成功] 依赖包检查通过
)
echo.

:: 检查数据库连接
echo [3/4] 检查数据库连接...
python -c "import pyodbc; pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost;DATABASE=DeviceDataSource;Trusted_Connection=yes;')" >nul 2>&1
if %errorLevel% neq 0 (
    echo    [警告] 数据库连接测试失败
    echo.
    echo    请确认:
    echo    1. SQL Server服务已启动
    echo    2. 数据库 DeviceDataSource 存在
    echo    3. 当前用户有数据库访问权限
    echo.
    echo    继续启动服务? (Y/N)
    set /p continue=
    if /i not "%continue%"=="Y" (
        echo    用户取消启动
        pause
        exit /b 1
    )
) else (
    echo    [成功] 数据库连接正常
)
echo.

:: 启动服务
echo [4/4] 启动应用服务...
echo.

:: 检查端口占用
netstat -an | findstr ":5000" >nul 2>&1
if %errorLevel% equ 0 (
    echo    [警告] 端口5000已被占用
    echo    是否强制停止占用进程? (Y/N)
    set /p kill_process=
    if /i "%kill_process%"=="Y" (
        for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000') do (
            taskkill /f /pid %%a >nul 2>&1
        )
        echo    [信息] 已停止占用进程
    )
)

netstat -an | findstr ":5001" >nul 2>&1
if %errorLevel% equ 0 (
    echo    [警告] 端口5001已被占用
    echo    是否强制停止占用进程? (Y/N)
    set /p kill_process=
    if /i "%kill_process%"=="Y" (
        for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5001') do (
            taskkill /f /pid %%a >nul 2>&1
        )
        echo    [信息] 已停止占用进程
    )
)

echo    启动主服务 (端口5000)...
start "华伍股份轨交车间数字化管理系统-主服务" cmd /k "chcp 65001 >nul && echo [主服务] 启动中... && python app.py"

:: 等待主服务启动
timeout /t 3 >nul

echo    启动管理服务 (端口5001)...
start "华伍股份轨交车间数字化管理系统-管理服务" cmd /k "chcp 65001 >nul && echo [管理服务] 启动中... && python admin_app.py"

:: 等待管理服务启动
timeout /t 3 >nul

echo.
echo ==========================================
echo           服务启动完成!
echo ==========================================
echo.
echo 访问地址:
echo   主监控页面: http://localhost:5000
echo   管理后台:   http://localhost:5001
echo.
echo 重要提示:
echo   1. 两个服务窗口已自动打开
echo   2. 关闭窗口即可停止对应服务
echo   3. 如需停止所有服务，运行 stop.bat
echo   4. 确保防火墙允许端口5000和5001
echo.
echo [%date% %time%] 系统启动完成
echo.

:: 询问是否打开浏览器
set /p open_browser=是否自动打开浏览器? (Y/N):
if /i "%open_browser%"=="Y" (
    echo    正在打开浏览器...
    start http://localhost:5000
)

echo.
echo 按任意键退出启动脚本...
pause >nul
