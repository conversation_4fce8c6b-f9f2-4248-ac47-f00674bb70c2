@echo off
:: 设置代码页为UTF-8，解决中文乱码问题
chcp 65001 >nul 2>&1

:: 设置窗口标题
title 华伍股份轨交车间数字化管理系统 - 停止脚本

:: 设置控制台颜色 (红色背景黑色字体)
color 0C

echo.
echo ==========================================
echo     停止华伍股份轨交车间数字化管理系统
echo     Stop Production Workshop Monitoring
echo ==========================================
echo.

:: 显示当前时间
echo [%date% %time%] 开始停止系统...
echo.

:: 确认停止操作
echo [警告] 即将停止所有相关服务
echo.
set /p confirm=确认停止系统? (Y/N):
if /i not "%confirm%"=="Y" (
    echo.
    echo [信息] 用户取消操作
    echo.
    pause
    exit /b 0
)

echo.
echo [1/3] 停止Python应用进程...

:: 查找并停止Python进程
set python_stopped=0
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq python.exe" /fo csv ^| find "python.exe"') do (
    set /a python_stopped+=1
    echo    停止进程: %%a
)

taskkill /f /im python.exe >nul 2>&1
if %errorLevel% equ 0 (
    echo    [成功] Python进程已停止 (共%python_stopped%个)
) else (
    echo    [信息] 没有运行中的Python进程
)

echo.
echo [2/3] 释放端口占用...

:: 检查并停止端口5000的进程
set port5000_found=0
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| findstr :5000') do (
    if not "%%a"=="0" (
        set port5000_found=1
        echo    释放端口5000，进程ID: %%a
        taskkill /f /pid %%a >nul 2>&1
    )
)

if %port5000_found% equ 0 (
    echo    [信息] 端口5000未被占用
) else (
    echo    [成功] 端口5000已释放
)

:: 检查并停止端口5001的进程
set port5001_found=0
for /f "tokens=5" %%a in ('netstat -aon 2^>nul ^| findstr :5001') do (
    if not "%%a"=="0" (
        set port5001_found=1
        echo    释放端口5001，进程ID: %%a
        taskkill /f /pid %%a >nul 2>&1
    )
)

if %port5001_found% equ 0 (
    echo    [信息] 端口5001未被占用
) else (
    echo    [成功] 端口5001已释放
)

echo.
echo [3/3] 清理临时文件...

:: 清理Python缓存文件
if exist "__pycache__" (
    rmdir /s /q "__pycache__" >nul 2>&1
    echo    [成功] 清理Python缓存文件
)

:: 清理临时日志文件
if exist "*.log" (
    del /q "*.log" >nul 2>&1
    echo    [成功] 清理日志文件
)

echo    [成功] 临时文件清理完成

echo.
echo ==========================================
echo           系统停止完成!
echo ==========================================
echo.

:: 最终验证
echo 最终状态检查:
netstat -an | findstr ":5000" >nul 2>&1
if %errorLevel% neq 0 (
    echo   端口5000: 已释放
) else (
    echo   端口5000: 仍被占用 (可能是其他程序)
)

netstat -an | findstr ":5001" >nul 2>&1
if %errorLevel% neq 0 (
    echo   端口5001: 已释放
) else (
    echo   端口5001: 仍被占用 (可能是其他程序)
)

tasklist /fi "imagename eq python.exe" | findstr "python.exe" >nul 2>&1
if %errorLevel% neq 0 (
    echo   Python进程: 已全部停止
) else (
    echo   Python进程: 仍有运行 (可能是其他Python程序)
)

echo.
echo [%date% %time%] 系统停止完成
echo.
echo 提示:
echo   1. 如需重新启动，运行 start.bat
echo   2. 如需完整部署，运行 deploy.bat
echo   3. 所有服务窗口应已自动关闭
echo.

echo 按任意键退出停止脚本...
pause >nul
